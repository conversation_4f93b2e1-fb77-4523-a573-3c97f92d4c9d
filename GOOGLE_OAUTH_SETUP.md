# Google OAuth 登录设置指南

## 概述

本项目已集成Google OAuth登录功能，用户可以使用Google账号快速登录游戏。

## 设置步骤

### 1. 创建Google Cloud项目

1. 访问 [Google Cloud Console](https://console.cloud.google.com/)
2. 创建新项目或选择现有项目
3. 启用Google+ API和Google OAuth2 API

### 2. 配置OAuth同意屏幕

1. 在Google Cloud Console中，导航到 "APIs & Services" > "OAuth consent screen"
2. 选择用户类型（内部或外部）
3. 填写应用信息：
   - 应用名称：您的游戏支付平台名称
   - 用户支持邮箱：您的支持邮箱
   - 开发者联系信息：您的邮箱地址
4. 添加授权域名（如果是外部应用）
5. 添加范围：
   - `openid`
   - `email`
   - `profile`

### 3. 创建OAuth客户端ID

1. 导航到 "APIs & Services" > "Credentials"
2. 点击 "Create Credentials" > "OAuth client ID"
3. 选择应用类型：Web application
4. 设置名称：例如 "Game Payment Portal Web Client"
5. 添加授权重定向URI：
   - 开发环境：`http://localhost:3000/auth/google/callback`
   - 生产环境：`https://yourdomain.com/auth/google/callback`
6. 保存并记录客户端ID和客户端密钥

### 4. 配置环境变量

1. 复制 `.env.example` 文件为 `.env.local`：
   ```bash
   cp .env.example .env.local
   ```

2. 在 `.env.local` 文件中填入您的Google OAuth配置：
   ```env
   # ✅ 客户端ID - 可以公开，会暴露给前端
   NEXT_PUBLIC_GOOGLE_CLIENT_ID=your_actual_google_client_id

   # 🔒 客户端密钥 - 必须保密，只在服务器端使用
   GOOGLE_CLIENT_SECRET=your_actual_google_client_secret

   # ✅ 重定向URI - 可以公开
   NEXT_PUBLIC_GOOGLE_REDIRECT_URI=http://localhost:3000/auth/google/callback
   ```

⚠️ **重要安全提示：**
- 绝对不要在客户端密钥前加 `NEXT_PUBLIC_` 前缀
- 确保 `.env.local` 文件在 `.gitignore` 中（已配置）
- 客户端密钥只能在服务器端API路由中使用

### 5. 后端API支持

确保您的后端API支持Google登录端点：

```
POST /admin-api/erp/game/google-login
```

请求参数：
```json
{
  "googleId": "用户的Google ID",
  "email": "用户邮箱",
  "name": "用户姓名",
  "picture": "用户头像URL",
  "gameId": "游戏ID",
  "productId": "产品ID",
  "accessToken": "Google访问令牌",
  "idToken": "Google ID令牌"
}
```

响应格式应与普通登录API相同。

## 功能特性

### 前端功能
- ✅ Google OAuth弹窗登录
- ✅ 多语言支持（中文、英文、韩文）
- ✅ 错误处理和用户反馈
- ✅ 响应式设计
- ✅ 与现有登录流程集成

### 安全特性
- ✅ 使用官方Google OAuth 2.0流程
- ✅ 客户端密钥仅在服务器端使用，永不暴露给前端
- ✅ 通过Next.js API路由安全处理令牌交换
- ✅ 访问令牌验证
- ✅ CSRF保护
- ✅ 环境变量安全管理

### 🔒 安全架构

```
前端 (浏览器)                    后端 (Next.js API)                Google OAuth
     │                                │                              │
     │ 1. 用户点击Google登录            │                              │
     ├─────────────────────────────────┤                              │
     │ 2. 打开Google OAuth弹窗          │                              │
     ├──────────────────────────────────────────────────────────────►│
     │                                │                              │ 3. 用户授权
     │◄─────────────────────────────────────────────────────────────┤
     │ 4. 获取授权码 (code)             │                              │
     ├─────────────────────────────────►│                              │
     │                                │ 5. 使用客户端密钥交换令牌        │
     │                                ├─────────────────────────────►│
     │                                │◄─────────────────────────────┤
     │◄─────────────────────────────────┤ 6. 返回访问令牌               │
     │ 7. 获取用户信息并登录             │                              │
```

**关键安全点：**
- 客户端密钥永远不会发送到前端
- 令牌交换在服务器端完成
- 前端只接收必要的访问令牌

## 测试

### 开发环境测试
1. 确保环境变量配置正确
2. 启动开发服务器：`npm run dev`
3. 访问任意游戏页面
4. 点击登录按钮
5. 选择"使用Google登录"
6. 完成Google OAuth流程

### 生产环境部署
1. 更新Google Cloud Console中的授权重定向URI
2. 更新生产环境的环境变量
3. 确保HTTPS配置正确
4. 测试完整的登录流程

## 故障排除

### 常见问题

1. **弹窗被阻止**
   - 确保浏览器允许弹窗
   - 检查弹窗阻止器设置

2. **重定向URI不匹配**
   - 检查Google Cloud Console中的重定向URI配置
   - 确保环境变量中的URI与Google配置一致

3. **客户端ID无效**
   - 验证环境变量中的客户端ID
   - 确保Google Cloud项目中的OAuth客户端已启用

4. **API错误**
   - 检查后端Google登录API是否正确实现
   - 验证API响应格式

### 调试技巧

1. 打开浏览器开发者工具查看控制台错误
2. 检查网络请求和响应
3. 验证环境变量是否正确加载
4. 测试Google OAuth流程的每个步骤

## 支持

如果您在设置过程中遇到问题，请：

1. 检查本文档的故障排除部分
2. 查看浏览器控制台的错误信息
3. 验证Google Cloud Console的配置
4. 确保后端API正确实现

## 更新日志

- **v1.0.0**: 初始Google OAuth登录功能实现
  - 支持弹窗式Google登录
  - 多语言界面支持
  - 与现有登录系统集成
