# 🔒 Google OAuth 安全检查清单

## 环境变量安全

### ✅ 正确的配置
```env
# ✅ 客户端ID - 可以公开
NEXT_PUBLIC_GOOGLE_CLIENT_ID=123456789.apps.googleusercontent.com

# ✅ 客户端密钥 - 必须保密
GOOGLE_CLIENT_SECRET=GOCSPX-abcdefghijklmnopqrstuvwxyz

# ✅ 重定向URI - 可以公开
NEXT_PUBLIC_GOOGLE_REDIRECT_URI=https://yourdomain.com/auth/google/callback
```

### ❌ 错误的配置
```env
# ❌ 绝对不要这样做！客户端密钥会暴露给前端
NEXT_PUBLIC_GOOGLE_CLIENT_SECRET=GOCSPX-abcdefghijklmnopqrstuvwxyz

# ❌ 不要在生产环境使用localhost
NEXT_PUBLIC_GOOGLE_REDIRECT_URI=http://localhost:3000/auth/google/callback
```

## 文件安全

### ✅ 检查清单

- [ ] `.env.local` 文件在 `.gitignore` 中
- [ ] 客户端密钥没有 `NEXT_PUBLIC_` 前缀
- [ ] 生产环境使用HTTPS重定向URI
- [ ] 定期轮换客户端密钥

### ❌ 常见错误

- [ ] 将 `.env.local` 提交到Git
- [ ] 在前端代码中硬编码密钥
- [ ] 在日志中打印敏感信息
- [ ] 使用HTTP重定向URI（生产环境）

## 代码安全

### ✅ 安全实践

```typescript
// ✅ 正确：在服务器端API路由中使用客户端密钥
export async function POST(request: NextRequest) {
  const clientSecret = process.env.GOOGLE_CLIENT_SECRET; // 只在服务器端
  // ...
}

// ✅ 正确：在前端使用客户端ID
const clientId = process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID;
```

### ❌ 安全风险

```typescript
// ❌ 错误：在前端使用客户端密钥
const clientSecret = process.env.GOOGLE_CLIENT_SECRET; // 这会是 undefined

// ❌ 错误：硬编码敏感信息
const clientSecret = "GOCSPX-abcdefghijklmnopqrstuvwxyz";
```

## Google Cloud Console 安全

### ✅ 配置检查

- [ ] 启用了必要的API（Google+ API, OAuth2 API）
- [ ] 重定向URI配置正确
- [ ] 使用HTTPS重定向URI（生产环境）
- [ ] 限制了OAuth客户端的使用范围
- [ ] 定期审查OAuth同意屏幕配置

### ⚠️ 生产环境额外检查

- [ ] 配置了正确的授权域名
- [ ] OAuth同意屏幕信息完整准确
- [ ] 设置了适当的范围权限
- [ ] 配置了品牌信息和隐私政策链接

## 运行时安全

### ✅ 监控和日志

```typescript
// ✅ 安全的日志记录
console.log('Google OAuth login attempt for user:', userEmail);

// ❌ 不安全的日志记录
console.log('Access token:', accessToken); // 永远不要记录令牌
```

### ✅ 错误处理

```typescript
// ✅ 安全的错误处理
catch (error) {
  console.error('OAuth error occurred'); // 不暴露敏感信息
  return { error: 'Authentication failed' }; // 通用错误消息
}
```

## 部署安全

### ✅ 生产环境检查

- [ ] 使用环境变量管理服务（如Vercel环境变量、AWS Secrets Manager）
- [ ] 启用HTTPS
- [ ] 配置正确的CORS策略
- [ ] 设置适当的CSP头
- [ ] 定期更新依赖包

### ✅ 监控

- [ ] 设置异常监控
- [ ] 监控OAuth失败率
- [ ] 记录安全相关事件
- [ ] 定期安全审计

## 应急响应

### 🚨 如果密钥泄露

1. **立即行动**
   - [ ] 在Google Cloud Console中撤销泄露的客户端密钥
   - [ ] 生成新的客户端密钥
   - [ ] 更新所有环境的环境变量

2. **调查和修复**
   - [ ] 检查Git历史是否包含敏感信息
   - [ ] 审查访问日志
   - [ ] 通知相关团队成员
   - [ ] 更新安全流程

3. **预防措施**
   - [ ] 加强代码审查
   - [ ] 实施自动化安全扫描
   - [ ] 培训团队成员安全意识

## 定期维护

### 📅 每月检查

- [ ] 审查OAuth客户端配置
- [ ] 检查环境变量安全性
- [ ] 更新依赖包
- [ ] 审查访问日志

### 📅 每季度检查

- [ ] 轮换客户端密钥
- [ ] 安全审计
- [ ] 更新安全文档
- [ ] 团队安全培训

---

**记住：安全是一个持续的过程，不是一次性的设置！**
