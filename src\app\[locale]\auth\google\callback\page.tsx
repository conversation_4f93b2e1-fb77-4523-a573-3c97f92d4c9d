"use client";

import { useEffect } from 'react';
import { useSearchParams } from 'next/navigation';

/**
 * Google OAuth 回调页面
 * 处理Google返回的授权码并发送给父窗口
 */
export default function GoogleCallbackPage() {
  const searchParams = useSearchParams();

  useEffect(() => {
    const code = searchParams.get('code');
    const error = searchParams.get('error');

    if (window.opener) {
      if (code) {
        // 成功获取授权码，发送给父窗口
        window.opener.postMessage({
          type: 'GOOGLE_AUTH_SUCCESS',
          code: code
        }, window.location.origin);
      } else if (error) {
        // 发生错误，发送错误信息给父窗口
        window.opener.postMessage({
          type: 'GOOGLE_AUTH_ERROR',
          error: error
        }, window.location.origin);
      } else {
        // 未知错误
        window.opener.postMessage({
          type: 'GOOGLE_AUTH_ERROR',
          error: '未知错误'
        }, window.location.origin);
      }
      
      // 关闭弹窗
      window.close();
    } else {
      // 如果不是在弹窗中打开，重定向到首页
      window.location.href = '/';
    }
  }, [searchParams]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-zinc-900 to-zinc-950 flex items-center justify-center">
      <div className="text-center">
        <div className="w-16 h-16 border-4 border-indigo-500/30 border-t-indigo-500 rounded-full animate-spin mx-auto mb-4"></div>
        <p className="text-white text-lg">正在处理登录...</p>
        <p className="text-zinc-400 text-sm mt-2">请稍候，窗口将自动关闭</p>
      </div>
    </div>
  );
}
