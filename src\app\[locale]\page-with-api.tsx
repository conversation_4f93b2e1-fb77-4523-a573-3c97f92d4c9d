"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import Image from "next/image";
import { useLocale } from "next-intl";
import Layout from "../components/Layout";
import { fetchGamesData, fetchSiteInfo, SiteInfo, GameListItem } from "../lib/games/api";
import { useExtractGameListData } from "../lib/games/utils";

export default function HomePage() {
  const locale = useLocale() as string;
  
  // 状态管理
  const [gameItems, setGameItems] = useState<GameListItem[] | null>(null);
  const [siteInfo, setSiteInfo] = useState<SiteInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // 从API获取站点信息和游戏数据
  useEffect(() => {
    async function loadData() {
      setLoading(true);
      try {
        // 并行请求站点信息和游戏数据
        const [siteData, gamesData] = await Promise.all([
          fetchSiteInfo(),
          fetchGamesData()
        ]);
        
        // 确保类型安全
        if ('i18n' in siteData && typeof siteData.id === 'number') {
          setSiteInfo(siteData as SiteInfo);
          setGameItems(gamesData);
          setError(null);
        } else {
          // 如果返回的不是预期的SiteInfo类型
          setError("站点信息格式错误");
          console.error("Unexpected site info format:", siteData);
        }
      } catch (err) {
        setError("加载数据失败");
        console.error(err);
      } finally {
        setLoading(false);
      }
    }
    
    loadData();
  }, []);
  
  // 转换游戏数据为前端使用的格式
  const games = useExtractGameListData(gameItems || []);
  
  // 加载状态
  if (loading) {
    return (
      <Layout>
        <div className="flex flex-col items-center justify-center py-20">
          <div className="w-16 h-16 border-4 border-indigo-500 border-t-transparent rounded-full animate-spin"></div>
          <p className="mt-4 text-zinc-400">加载中...</p>
        </div>
      </Layout>
    );
  }
  
  // 错误状态
  if (error || !games) {
    return (
      <Layout>
        <div className="flex flex-col items-center justify-center py-20">
          <h1 className="text-2xl font-bold text-white mb-4">网站异常</h1>
          <p className="text-zinc-400 mb-8">{error || "服务器错误，请稍后再试"}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-6 py-3 bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white font-medium rounded-lg transition-colors"
          >
            重新加载
          </button>
        </div>
      </Layout>
    );
  }
  
  // 获取站点标题和描述
  const siteTitle = siteInfo?.i18n?.title[locale] || '';
  const siteDescription = siteInfo?.i18n?.description[locale] || '';
  
  return (
    <Layout>
      {/* 如果有站点信息，显示站点标题 */}
      {siteInfo && siteInfo.i18n && (
        <section className="mb-8">
          <h1 className="text-4xl font-bold text-white mb-4">
            {siteTitle}
          </h1>
          <p className="text-zinc-400">
            {siteDescription}
          </p>
        </section>
      )}
      
      <section className="mb-12">
        <h1 className="text-3xl font-bold text-white mb-6">热门游戏</h1>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {games.map((game) => (
            <Link
              key={game.id}
              href={`/games/${game.id}`}
              className="bg-zinc-800 rounded-xl overflow-hidden hover:bg-zinc-700/80 transition-colors group"
            >
              <div className="relative h-64 sm:h-72">
                <Image
                  src={game.bannerUrl}
                  alt={game.title}
                  fill
                  className="object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent" />
                <div className="absolute bottom-0 left-0 right-0 p-4">
                  <h2 className="text-xl font-bold text-white mb-1 group-hover:text-indigo-300 transition-colors">
                    {game.title}
                  </h2>
                  {game.category && (
                    <div className="flex items-center space-x-3">
                      <span className="text-xs font-semibold px-2 py-1 rounded-full bg-indigo-500/20 text-indigo-300">
                        {game.category}
                      </span>
                      <span className="text-xs text-zinc-300 flex items-center">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-3 w-3 mr-1 text-yellow-400"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                          strokeWidth={2}
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"
                          />
                        </svg>
                        {game.rating}
                      </span>
                    </div>
                  )}
                  {!game.category && (
                    <div className="flex items-center space-x-3">
                      <span className="text-xs text-zinc-300 flex items-center">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-3 w-3 mr-1 text-yellow-400"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                          strokeWidth={2}
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"
                          />
                        </svg>
                        {game.rating}
                      </span>
                    </div>
                  )}
                </div>
              </div>
              <div className="p-4">
                {game.description && (
                  <p className="text-zinc-400 text-sm line-clamp-2">
                    {game.description}
                  </p>
                )}
                {!game.description && (
                  <p className="text-zinc-400 text-sm line-clamp-2">
                    点击查看详情
                  </p>
                )}
                <div className="mt-4 flex items-center justify-between">
                  <span className="text-sm text-zinc-300">
                    {game.products?.length ? `${game.products.length} 个充值项目` : '查看充值项目'}
                  </span>
                  <span className="text-sm font-medium text-indigo-400 group-hover:text-indigo-300 transition-colors">
                    查看详情
                  </span>
                </div>
              </div>
            </Link>
          ))}
        </div>
      </section>
    </Layout>
  );
} 