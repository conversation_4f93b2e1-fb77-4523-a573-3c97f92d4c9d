"use client";

import { useState } from "react";
import { getSiteConfig, getGoogleClientId, checkGoogleConfig } from "../lib/config/site-config";
import { openGoogleSignInPopup, parseJWTCredential } from "../lib/auth/google-gsi";
import { SITE_ID } from "../config/api";

interface GoogleConfigTestProps {
  siteId?: number;
}

export default function GoogleConfigTest({ siteId }: GoogleConfigTestProps) {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<string>("");

  const currentSiteId = siteId || SITE_ID;

  const testGetSiteConfig = async () => {
    setLoading(true);
    try {
      const config = await getSiteConfig(currentSiteId);
      setResult(`站点配置获取成功 (siteId: ${currentSiteId}):\n${JSON.stringify(config, null, 2)}`);
    } catch (error) {
      setResult(`站点配置获取失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setLoading(false);
    }
  };

  const testGetGoogleClientId = async () => {
    setLoading(true);
    try {
      const clientId = await getGoogleClientId(currentSiteId);
      setResult(`Google Client ID 获取成功 (siteId: ${currentSiteId}):\n${clientId}`);
    } catch (error) {
      setResult(`Google Client ID 获取失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setLoading(false);
    }
  };

  const testCheckGoogleConfig = async () => {
    setLoading(true);
    try {
      const configCheck = await checkGoogleConfig(currentSiteId);
      setResult(`Google配置检查结果 (siteId: ${currentSiteId}):\n${JSON.stringify(configCheck, null, 2)}`);
    } catch (error) {
      setResult(`Google配置检查失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setLoading(false);
    }
  };

  const testGoogleLogin = async () => {
    setLoading(true);
    try {
      setResult('正在打开Google登录弹窗...');
      const credential = await openGoogleSignInPopup(currentSiteId);
      const userInfo = parseJWTCredential(credential);
      setResult(`Google登录测试成功 (siteId: ${currentSiteId}):\n用户信息: ${JSON.stringify(userInfo, null, 2)}\n\nCredential: ${credential.substring(0, 100)}...`);
    } catch (error) {
      setResult(`Google登录测试失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="bg-zinc-800/50 backdrop-blur-sm border border-zinc-700/30 rounded-xl p-6 mb-8">
      <h3 className="text-xl font-semibold text-white mb-4">Google配置测试</h3>
      
      <div className="flex flex-wrap gap-3 mb-4">
        <button
          onClick={testGetSiteConfig}
          disabled={loading}
          className="px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-800 text-white rounded-lg transition-colors"
        >
          {loading ? "测试中..." : "测试获取站点配置"}
        </button>
        
        <button
          onClick={testGetGoogleClientId}
          disabled={loading}
          className="px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-green-800 text-white rounded-lg transition-colors"
        >
          {loading ? "测试中..." : "测试获取Google Client ID"}
        </button>
        
        <button
          onClick={testCheckGoogleConfig}
          disabled={loading}
          className="px-4 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-purple-800 text-white rounded-lg transition-colors"
        >
          {loading ? "测试中..." : "测试Google配置检查"}
        </button>

        <button
          onClick={testGoogleLogin}
          disabled={loading}
          className="px-4 py-2 bg-red-600 hover:bg-red-700 disabled:bg-red-800 text-white rounded-lg transition-colors"
        >
          {loading ? "测试中..." : "测试Google弹窗登录"}
        </button>
      </div>
      
      {result && (
        <div className="bg-zinc-900/50 border border-zinc-600/30 rounded-lg p-4">
          <h4 className="text-sm font-medium text-zinc-300 mb-2">测试结果:</h4>
          <pre className="text-xs text-zinc-400 whitespace-pre-wrap overflow-x-auto">
            {result}
          </pre>
        </div>
      )}
    </div>
  );
}
