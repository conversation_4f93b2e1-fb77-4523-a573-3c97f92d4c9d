"use client";

import { useState, useEffect, useRef } from "react";
import Link from "next/link";
import LanguageSwitcher from "./LanguageSwitcher";
import CustomerServiceButton from "./CustomerServiceButton";
import { usePathname, useRouter } from "next/navigation";
import { useLocale, useTranslations } from "next-intl";

interface LayoutProps {
  children: React.ReactNode;
}

export default function Layout({ children }: LayoutProps) {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const mainContentRef = useRef<HTMLDivElement>(null);
  const t = useTranslations();
  const locale = useLocale();

  // 监听滚动事件，当页面滚动时改变导航栏样式
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 10) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };

    window.addEventListener("scroll", handleScroll, { passive: true });
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // 移动端菜单打开时阻止背景滚动
  useEffect(() => {
    if (isMenuOpen) {
      // 防止背景滚动
      document.body.style.overflow = 'hidden';
    } else {
      // 恢复滚动
      document.body.style.overflow = '';
    }
    
    return () => {
      // 清理函数
      document.body.style.overflow = '';
    };
  }, [isMenuOpen]);

  // 移动端滚动优化 - 简化处理，避免干扰正常滚动
  useEffect(() => {
    // 仅在移动设备上应用基本优化
    if (typeof window !== 'undefined' && window.innerWidth <= 768) {
      const contentEl = mainContentRef.current;
      if (contentEl) {
        // 只设置基本的滚动优化样式
        contentEl.style.webkitOverflowScrolling = 'touch';
        contentEl.style.transform = 'translateZ(0)';

        return () => {
          contentEl.style.webkitOverflowScrolling = '';
          contentEl.style.transform = '';
        };
      }
    }
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-zinc-900 to-zinc-950 text-white">
      <header 
        className={`sticky top-0 z-30 transition-all duration-300 ${
          isScrolled 
            ? "bg-zinc-900/90 backdrop-blur-lg shadow-lg" 
            : "bg-transparent"
        }`}
      >
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <Link href={`/${locale}`} className="flex items-center space-x-2 group">
            <div className="relative w-8 h-8 transition-transform duration-300 group-hover:scale-110">
              <div className="absolute inset-0 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full blur-sm opacity-80 animate-pulse"></div>
              <div className="absolute inset-0.5 bg-zinc-900 rounded-full"></div>
              <div className="absolute inset-2 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full"></div>
            </div>
            <span className="text-xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-indigo-400 to-purple-400 transition-all duration-300 group-hover:from-indigo-300 group-hover:to-purple-300">
              GamePay
            </span>
          </Link>

          {/* 桌面端导航 */}
          <nav className="hidden md:flex items-center space-x-8">
            <NavLink href={`/${locale}`} label={t('nav.home')} />
            <NavLink href={`/${locale}/games`} label={t('nav.games')} />
            <NavLink href={`/${locale}/about`} label={t('footer.aboutUs')} />
            <NavLink href={`/${locale}/help`} label={t('footer.helpCenter')} />
            <CustomerServiceButton variant="nav" />
          </nav>

          <div className="flex items-center space-x-4">
            {/* 语言切换器 - 仅在桌面端显示 */}
            <div className="relative z-20 hidden md:block">
              <LanguageSwitcher />
            </div>
            
            {/* 移动端菜单按钮 */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="md:hidden p-2 rounded-md hover:bg-zinc-800 transition-colors"
              aria-label={isMenuOpen ? t('nav.menuClose') : t('nav.menuOpen')}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                {isMenuOpen ? (
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                ) : (
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 6h16M4 12h16M4 18h16"
                  />
                )}
              </svg>
            </button>
          </div>
        </div>

        {/* 移动端菜单 */}
        <div
          className={`md:hidden fixed inset-0 bg-zinc-900/95 z-50 transform transition-transform duration-300 ease-in-out ${
            isMenuOpen ? "translate-x-0" : "translate-x-full"
          }`}
        >
          <div className="flex flex-col h-full">
            <div className="flex items-center justify-between p-4 border-b border-zinc-800">
              <Link href={`/${locale}`} className="flex items-center space-x-2">
                <div className="relative w-8 h-8">
                  <div className="absolute inset-0 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full blur-sm opacity-80"></div>
                  <div className="absolute inset-0.5 bg-zinc-900 rounded-full"></div>
                  <div className="absolute inset-2 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full"></div>
                </div>
                <span className="text-xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-indigo-400 to-purple-400">
                  GamePay
                </span>
              </Link>
              <button
                onClick={() => setIsMenuOpen(false)}
                className="p-2 rounded-md hover:bg-zinc-800 transition-colors"
                aria-label={t('nav.menuClose')}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-6 w-6"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>
            
            <div className="flex-1 overflow-y-auto p-4">
              <nav className="flex flex-col space-y-4">
                <MobileNavLink href={`/${locale}`} label={t('nav.home')} onClick={() => setIsMenuOpen(false)} />
                <MobileNavLink href={`/${locale}/games`} label={t('nav.games')} onClick={() => setIsMenuOpen(false)} />
                <MobileNavLink href={`/${locale}/about`} label={t('footer.aboutUs')} onClick={() => setIsMenuOpen(false)} />
                <MobileNavLink href={`/${locale}/help`} label={t('footer.helpCenter')} onClick={() => setIsMenuOpen(false)} />

                {/* 移动端客服按钮 */}
                <CustomerServiceButton
                  variant="nav"
                  className="w-full justify-start text-base font-medium text-zinc-300 hover:bg-zinc-800/40 hover:text-white rounded-lg px-3 py-2"
                />

                {/* 移动端语言切换选项 */}
                <div className="mt-4 pt-4 border-t border-zinc-700/50">
                  <div className="px-3 py-2 text-sm font-medium text-zinc-400">{t('nav.languageSelect')}</div>
                  <MobileLanguageOptions onClose={() => setIsMenuOpen(false)} />
                </div>
              </nav>
            </div>
          </div>
        </div>
      </header>

      <main
        ref={mainContentRef}
        className="container mx-auto px-4 py-8 relative"
        style={{
          minHeight: '100vh',
          paddingBottom: '120px', // 为悬浮支付栏预留空间
          position: 'relative',
          zIndex: 1
        }}
      >
        {children}
      </main>

      {/* 移动端美观页脚，两列布局，防止被悬浮支付栏遮挡 */}
      <div className="block md:hidden bg-zinc-900/80 border-t border-zinc-800/50 pt-8 pb-28 rounded-t-2xl shadow-2xl">
        <div className="max-w-sm mx-auto px-4 flex flex-col items-center">
          {/* Logo */}
          <div className="flex justify-center mb-6">
            <Link href={`/${locale}`} className="flex items-center space-x-2">
              <div className="relative w-8 h-8">
                <div className="absolute inset-0 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full blur opacity-80"></div>
                <div className="absolute inset-1 bg-zinc-900 rounded-full"></div>
              </div>
              <span className="text-xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-indigo-400 to-purple-400">
                GamePay
              </span>
            </Link>
          </div>
          {/* 两列栏目 */}
          <div className="w-full flex flex-row justify-center gap-8 mb-6">
            {/* 左列 关于我们 */}
            <div>
              <h3 className="text-sm font-semibold text-zinc-300 mb-2">{t('footer.aboutUs')}</h3>
              <ul className="space-y-2">
                <li>
                  <Link href={`/${locale}/about`} className="text-xs text-zinc-400 hover:text-white transition-colors">{t('footer.companyIntro')}</Link>
                </li>
                <li>
                  <Link href={`/${locale}/about`} className="text-xs text-zinc-400 hover:text-white transition-colors">{t('footer.joinUs')}</Link>
                </li>
                <li>
                  <Link href={`/${locale}/about`} className="text-xs text-zinc-400 hover:text-white transition-colors">{t('footer.contactUs')}</Link>
                </li>
              </ul>
            </div>
            {/* 右列 帮助中心 */}
            <div>
              <h3 className="text-sm font-semibold text-zinc-300 mb-2">{t('footer.helpCenter')}</h3>
              <ul className="space-y-2">
                <li>
                  <Link href={`/${locale}/help/faq`} className="text-xs text-zinc-400 hover:text-white transition-colors">{t('footer.faq')}</Link>
                </li>
                <li>
                  <Link href={`/${locale}/help/payment-guide`} className="text-xs text-zinc-400 hover:text-white transition-colors">{t('footer.paymentGuide')}</Link>
                </li>
                <li>
                  <Link href={`/${locale}/help/refund-policy`} className="text-xs text-zinc-400 hover:text-white transition-colors">{t('footer.refundPolicy')}</Link>
                </li>
              </ul>
            </div>
          </div>
          {/* 关注我们 */}
          <div className="w-full mb-6">
            <h3 className="text-sm font-semibold text-zinc-300 mb-2 text-center">{t('footer.followUs')}</h3>
            <div className="flex justify-center space-x-4">
              <a href="#" className="p-2 rounded-full bg-zinc-800 hover:bg-indigo-600 transition-colors shadow-md">
                {/* Twitter */}
                <svg className="h-5 w-5 text-zinc-400" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
                </svg>
              </a>
              <a href="#" className="p-2 rounded-full bg-zinc-800 hover:bg-indigo-600 transition-colors shadow-md">
                {/* Instagram */}
                <svg className="h-5 w-5 text-zinc-400" fill="currentColor" viewBox="0 0 24 24">
                  <path fillRule="evenodd" d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z" clipRule="evenodd" />
                </svg>
              </a>
              <a href="#" className="p-2 rounded-full bg-zinc-800 hover:bg-indigo-600 transition-colors shadow-md">
                {/* GitHub */}
                <svg className="h-5 w-5 text-zinc-400" fill="currentColor" viewBox="0 0 24 24">
                  <path fillRule="evenodd" d="M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z" clipRule="evenodd" />
                </svg>
              </a>
            </div>
          </div>
          {/* 版权信息 */}
          <div className="text-center text-xs text-zinc-500 mt-6">
            © {new Date().getFullYear()} GamePay. {t('footer.allRightsReserved')}
          </div>
        </div>
      </div>
      {/* 桌面端页脚保持原样 */}
      <footer className="hidden md:block bg-zinc-900/70 border-t border-zinc-800/50 py-8 mt-16">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <Link href={`/${locale}`} className="flex items-center space-x-2 mb-4">
                <div className="relative w-6 h-6">
                  <div className="absolute inset-0 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full blur-sm opacity-80"></div>
                  <div className="absolute inset-0.5 bg-zinc-900 rounded-full"></div>
                  <div className="absolute inset-1.5 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full"></div>
                </div>
                <span className="text-lg font-bold bg-clip-text text-transparent bg-gradient-to-r from-indigo-400 to-purple-400">
                  GamePay
                </span>
              </Link>
              <p className="text-sm text-zinc-400">
                {t('footer.description')}
              </p>
            </div>
            <div>
              <h3 className="text-sm font-semibold text-zinc-300 mb-4">{t('footer.aboutUs')}</h3>
              <ul className="space-y-2">
                <li>
                  <Link href={`/${locale}/about`} className="text-xs text-zinc-400 hover:text-white transition-colors">
                    {t('footer.companyIntro')}
                  </Link>
                </li>
                <li>
                  <Link href={`/${locale}/about`} className="text-xs text-zinc-400 hover:text-white transition-colors">
                    {t('footer.joinUs')}
                  </Link>
                </li>
                <li>
                  <Link href={`/${locale}/about`} className="text-xs text-zinc-400 hover:text-white transition-colors">
                    {t('footer.contactUs')}
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h3 className="text-sm font-semibold text-zinc-300 mb-4">{t('footer.helpCenter')}</h3>
              <ul className="space-y-2">
                <li>
                  <Link href={`/${locale}/help/faq`} className="text-xs text-zinc-400 hover:text-white transition-colors">
                    {t('footer.faq')}
                  </Link>
                </li>
                <li>
                  <Link href={`/${locale}/help/payment-guide`} className="text-xs text-zinc-400 hover:text-white transition-colors">
                    {t('footer.paymentGuide')}
                  </Link>
                </li>
                <li>
                  <Link href={`/${locale}/help/refund-policy`} className="text-xs text-zinc-400 hover:text-white transition-colors">
                    {t('footer.refundPolicy')}
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h3 className="text-sm font-semibold text-zinc-300 mb-4">{t('footer.followUs')}</h3>
              <div className="flex space-x-4">
                <a href="#" className="p-2 rounded-full bg-zinc-800 hover:bg-zinc-700 transition-colors">
                  <svg className="h-4 w-4 text-zinc-400" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
                  </svg>
                </a>
                <a href="#" className="p-2 rounded-full bg-zinc-800 hover:bg-zinc-700 transition-colors">
                  <svg className="h-4 w-4 text-zinc-400" fill="currentColor" viewBox="0 0 24 24">
                    <path fillRule="evenodd" d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z" clipRule="evenodd" />
                  </svg>
                </a>
                <a href="#" className="p-2 rounded-full bg-zinc-800 hover:bg-zinc-700 transition-colors">
                  <svg className="h-4 w-4 text-zinc-400" fill="currentColor" viewBox="0 0 24 24">
                    <path fillRule="evenodd" d="M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z" clipRule="evenodd" />
                  </svg>
                </a>
              </div>
            </div>
          </div>
          <div className="mt-8 pt-6 border-t border-zinc-800 text-xs text-zinc-500 text-center">
            © {new Date().getFullYear()} GamePay. {t('footer.allRightsReserved')}
          </div>
        </div>
      </footer>

      {/* 右下角悬浮客服按钮 */}
      <CustomerServiceButton variant="floating" />
    </div>
  );
}

function NavLink({ href, label }: { href: string; label: string }) {
  const pathname = usePathname();

  const isActive = pathname === href;

  return (
    <Link
      href={href}
      className={`text-sm font-medium transition-colors ${
        isActive
          ? "text-white"
          : "text-zinc-400 hover:text-white"
      }`}
    >
      {label}
    </Link>
  );
}

function MobileNavLink({ href, label, onClick }: { href: string; label: string; onClick: () => void }) {
  const pathname = usePathname();

  const isActive = pathname === href;

  return (
    <Link
      href={href}
      onClick={onClick}
      className={`px-3 py-2 rounded-lg text-base font-medium transition-colors ${
        isActive
          ? "bg-zinc-800/70 text-white"
          : "text-zinc-300 hover:bg-zinc-800/40 hover:text-white"
      }`}
    >
      {label}
    </Link>
  );
}

function MobileLanguageOptions({ onClose }: { onClose: () => void }) {
  const pathname = usePathname();
  const router = useRouter();
  const currentLocale = useLocale();
  
  // 获取当前路径，去除语言前缀
  const pathnameWithoutLocale = pathname.replace(`/${currentLocale}`, "");
  
  const handleLocaleChange = (newLocale: string) => {
    // 构建新的URL路径，添加新的语言前缀
    const newPath = `/${newLocale}${pathnameWithoutLocale || ""}`;
    router.push(newPath);
    onClose();
  };
  
  return (
    <div className="flex flex-col space-y-1">
      {['zh', 'en', 'ko'].map((locale) => (
        <button
          key={locale}
          onClick={() => handleLocaleChange(locale)}
          className={`px-3 py-2 rounded-lg text-left flex items-center justify-between ${
            currentLocale === locale
              ? "bg-zinc-800/70 text-white"
              : "text-zinc-300 hover:bg-zinc-800/40 hover:text-white"
          }`}
        >
          <span>
            {locale === 'zh' ? '中文' : locale === 'en' ? 'English' : '한국어'}
          </span>
          {currentLocale === locale && (
            <svg className="h-4 w-4 text-indigo-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          )}
        </button>
      ))}
    </div>
  );
} 