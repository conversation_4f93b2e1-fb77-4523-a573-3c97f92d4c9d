"use client";

import { useState, useEffect } from "react";
import * as Dialog from "@radix-ui/react-dialog";
import axios from "axios";
import { useTranslations } from "next-intl";
import { Game } from "../lib/games/types";
import {API_BASE_URL, API_ENDPOINTS, SITE_ID} from "../config/api";
import { getUserSiteInfo } from "../lib/games/api";
import { signInWithGoogleButton, signInWithGoogleButtonFallback, parseJWTCredential } from "../lib/auth/google-gsi";

interface LoginModalProps {
  isOpen: boolean;
  onClose: () => void;
  game: Game;
  productId?: string;
  serverUrl: string;
  siteId?: number; // 添加 siteId 参数
}

// 定义登录响应类型
interface LoginResponse {
  code: number;
  data: {
    uid: number;
    username: string;
    productName: string | null;
    productId: number;
    channelName: string | null;
    isGuest: number;
    userStatus: number;
    regTime: number;
    totalAmount: number;
    loginTotal: number | null;
    deviceId: string | null;
    callbackUrl: string;
    callbackKey: string;
    productCode: string;
  };
  msg: string;
}

export default function LoginModal({ isOpen, onClose, game, productId, serverUrl, siteId }: LoginModalProps) {
  const [userName, setUserName] = useState("");
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const [googleLoading, setGoogleLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const t = useTranslations();

  // 检查是否有全局登录信息，如果有则预填充用户名
  useEffect(() => {
    if (isOpen) {
      const globalUser = localStorage.getItem('globalUser');
      if (globalUser) {
        try {
          const globalUserData = JSON.parse(globalUser);
          const loginTime = globalUserData.loginTime || 0;
          const currentTime = Date.now();
          const twentyFourHours = 24 * 60 * 60 * 1000;

          if (currentTime - loginTime < twentyFourHours && globalUserData.username) {
            setUserName(globalUserData.username);
          }
        } catch (e) {
          console.error('解析全局用户信息失败:', e);
        }
      }
    }
  }, [isOpen]);



  // 完成登录流程
  const completeLogin = async (loginUserData: LoginResponse['data'], serverId: string, roleId: string) => {
    // 保存全局用户基础信息（跨游戏共享）
    const globalUserInfo = {
      uid: loginUserData.uid,
      username: loginUserData.username,
      isGuest: loginUserData.isGuest,
      userStatus: loginUserData.userStatus,
      callbackUrl: loginUserData.callbackUrl,
      callbackKey: loginUserData.callbackKey,
      loginTime: Date.now() // 添加登录时间戳
    };
    localStorage.setItem('globalUser', JSON.stringify(globalUserInfo));

    // 保存当前游戏特定的用户数据
    const gameSpecificUserInfo = {
      ...globalUserInfo,
      productId: loginUserData.productId,
      gameId: game.id,
      productCode: loginUserData.productCode,
      serverId: serverId,
      roleId: roleId
    };
    localStorage.setItem(`gameUser_${game.id}`, JSON.stringify(gameSpecificUserInfo));

    // 为了向后兼容，也保存到原来的 gameUser 键
    localStorage.setItem('gameUser', JSON.stringify(gameSpecificUserInfo));

    // 关闭模态框
    onClose();

    // 刷新页面
    window.location.reload();
  };





  // Google登录处理函数
  const handleGoogleLogin = async () => {
    setGoogleLoading(true);
    setError(null);

    try {
      // 智能Google登录：先尝试一键登录，失败则回退到按钮式登录
      let credential: string;

      try {
        console.log('尝试一键Google登录...');
        credential = await signInWithGoogleButton();
      } catch (error) {
        console.log('一键登录失败，回退到按钮式登录:', error);
        console.log('显示Google登录按钮...');
        credential = await signInWithGoogleButtonFallback();
      }

      // 解析JWT credential获取用户信息
      const userInfo = parseJWTCredential(credential);

      if (!userInfo) {
        setError(t('login.googleAuthFailed'));
        return;
      }

      console.log('Google用户信息:', userInfo);

      // 调用Java后端Google登录API，传递credential
      const response = await axios.post<LoginResponse>(`${API_BASE_URL}/erp/game/google-login`, {
        access_token: credential, // 传递JWT credential
        googleId: userInfo.sub, // Google用户ID在JWT中是'sub'字段
        email: userInfo.email,
        name: userInfo.name,
        picture: userInfo.picture,
        gameId: game.id,
        productId: productId,
        siteId: siteId || SITE_ID // 使用传入的 siteId，如果没有则使用默认值
      });

      if (response.data.code === 0) {
        const loginUserData = response.data.data;

        if (!loginUserData || !loginUserData.username || !loginUserData.uid) {
          setError(t('login.invalidCredentials'));
          return;
        }

        try {
          if (!serverUrl) {
            setError(t('login.serverError'));
            return;
          }

          // 获取区服角色信息
          const siteInfo = await getUserSiteInfo(serverUrl, {
            uid: loginUserData.uid,
            username: loginUserData.username,
            productCode: loginUserData.productCode,
            callbackKey: loginUserData.callbackKey
          });

          localStorage.setItem('serverRoleList', JSON.stringify(siteInfo));
          await completeLogin(loginUserData, '', '');
        } catch (error) {
          console.error("获取区服角色信息失败:", error);
          setError(t('login.getServerRoleInfoFailed'));
        }
      } else {
        setError(t('login.googleLoginError'));
      }
    } catch (error) {
      console.error("Google登录失败:", error);
      if (error instanceof Error) {
        if (error.message.includes('取消')) {
          setError(t('login.googleAuthCancelled'));
        } else if (error.message.includes('弹窗')) {
          setError(t('login.popupBlocked'));
        } else {
          setError(t('login.googleAuthFailed'));
        }
      } else {
        setError(t('login.googleAuthFailed'));
      }
    } finally {
      setGoogleLoading(false);
    }
  };

  const handleLogin = async () => {
    // 验证输入
    if (!userName.trim()) {
      setError(t('login.usernameRequired'));
      return;
    }

    if (!password.trim()) {
      setError(t('login.passwordRequired'));
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // 调用实际的登录API
      const response = await axios.post<LoginResponse>(`${API_BASE_URL}/erp/game/login`, {
        username: userName,
        password: password,
        gameId: game.id,
        productId: productId,
        siteId: siteId || SITE_ID // 使用传入的 siteId，如果没有则使用默认值
      });

      // 检查响应状态
      if (response.data.code === 0) {
        // 登录成功
        const loginUserData = response.data.data;

        // 检查userData是否为null
        if (!loginUserData || !loginUserData.username || !loginUserData.uid) {
          setError(t('login.invalidCredentials'));
          return;
        }



        try {
          // 使用传入的 serverUrl
          if (!serverUrl) {
            setError(t('login.serverError'));
            return;
          }

          // 获取区服角色信息
          const siteInfo = await getUserSiteInfo(serverUrl, {
            uid: loginUserData.uid,
            username: loginUserData.username,
            productCode: loginUserData.productCode,
            callbackKey: loginUserData.callbackKey
          });

          // 保存区服角色信息到localStorage，供游戏页面使用
          localStorage.setItem('serverRoleList', JSON.stringify(siteInfo));

          // 直接完成登录，不预选择区服角色
          await completeLogin(loginUserData, '', '');
        } catch (error) {
          console.error("获取区服角色信息失败:", error);
          setError(t('login.getServerRoleInfoFailed'));
        }
      } else {
        // 登录失败，显示错误信息
        setError(t('login.invalidCredentials'));
      }
    } catch (error) {
      if (axios.isAxiosError(error) && error.response) {
        setError(error.response.data?.msg || t('login.serverError'));
      } else {
        setError(t('login.networkError'));
      }
      console.error("登录请求失败:", error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog.Root open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <Dialog.Portal>
        <Dialog.Overlay 
          className="fixed inset-0 bg-black/60 backdrop-blur-sm z-40" 
          style={{ touchAction: 'none' }}
          onClick={(e) => e.stopPropagation()}
        />
        <Dialog.Content 
          className="fixed left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-full max-w-md max-h-[85vh] overflow-auto rounded-2xl bg-gradient-to-br from-zinc-900 to-zinc-950 border border-zinc-800 shadow-2xl z-50 p-6"
          style={{ touchAction: 'pan-y' }}
          onClick={(e) => e.stopPropagation()}
        >
          <div className="flex justify-between items-start mb-6">
            <Dialog.Title className="text-xl font-bold text-white">
              {game.title || game.id} {t('login.title')}
            </Dialog.Title>
            <Dialog.Close asChild>
              <button
                className="rounded-full p-1 hover:bg-zinc-800 transition-colors"
                aria-label={t('login.close')}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5 text-zinc-400"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </Dialog.Close>
          </div>

          <div className="space-y-4">
            {error && (
              <div className="p-3 bg-red-500/20 border border-red-500/30 rounded-lg text-red-400 text-sm">
                {error}
              </div>
            )}

            <div>
              <label className="block text-sm font-medium text-zinc-400 mb-1">
                {t('login.username')}
              </label>
              <input
                type="text"
                value={userName}
                onChange={(e) => setUserName(e.target.value)}
                placeholder={t('login.usernamePlaceholder')}
                className="w-full px-3 py-2 bg-zinc-900 border border-zinc-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-indigo-500/50"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-zinc-400 mb-1">
                {t('login.password')}
              </label>
              <input
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder={t('login.passwordPlaceholder')}
                className="w-full px-3 py-2 bg-zinc-900 border border-zinc-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-indigo-500/50"
              />
            </div>

            <button
              onClick={handleLogin}
              disabled={loading || googleLoading}
              className={`w-full py-3 px-4 mt-2 bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white font-medium rounded-lg transition-colors flex items-center justify-center ${
                loading || googleLoading ? "opacity-70 cursor-not-allowed" : ""
              }`}
            >
              {loading ? (
                <>
                  <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2"></div>
                  {t('login.loggingIn')}
                </>
              ) : (
                t('login.loginButton')
              )}
            </button>

            {/* 分隔线 */}
            <div className="flex items-center my-4">
              <div className="flex-1 border-t border-zinc-700"></div>
              <span className="px-3 text-sm text-zinc-400">{t('login.orDivider')}</span>
              <div className="flex-1 border-t border-zinc-700"></div>
            </div>

            {/* Google登录按钮 */}
            <button
              onClick={handleGoogleLogin}
              disabled={loading || googleLoading}
              className={`w-full py-3 px-4 bg-white hover:bg-gray-50 text-gray-900 font-medium rounded-lg transition-colors flex items-center justify-center border border-gray-300 ${
                loading || googleLoading ? "opacity-70 cursor-not-allowed" : ""
              }`}
            >
              {googleLoading ? (
                <>
                  <div className="w-5 h-5 border-2 border-gray-300 border-t-gray-900 rounded-full animate-spin mr-2"></div>
                  {t('login.googleLoggingIn')}
                </>
              ) : (
                <>
                  <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24">
                    <path
                      fill="#4285F4"
                      d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                    />
                    <path
                      fill="#34A853"
                      d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                    />
                    <path
                      fill="#FBBC05"
                      d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                    />
                    <path
                      fill="#EA4335"
                      d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                    />
                  </svg>
                  {t('login.googleLogin')}
                </>
              )}
            </button>
          </div>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
} 