/**
 * 前端Google OAuth认证工具函数
 * 当服务器端网络有问题时使用此版本
 */

import { GOOGLE_OAUTH_CONFIG } from '../../config/api';

// Google OAuth 响应类型
export interface GoogleAuthResponse {
  access_token: string;
  id_token: string;
  scope: string;
  token_type: string;
  expires_in: number;
}

// Google 用户信息类型
export interface GoogleUserInfo {
  id: string;
  email: string;
  verified_email: boolean;
  name: string;
  given_name: string;
  family_name: string;
  picture: string;
  locale: string;
}

/**
 * 生成Google OAuth登录URL
 */
export function generateGoogleAuthUrl(): string {
  const params = new URLSearchParams({
    client_id: GOOGLE_OAUTH_CONFIG.CLIENT_ID,
    redirect_uri: GOOGLE_OAUTH_CONFIG.REDIRECT_URI,
    response_type: 'code',
    scope: GOOGLE_OAUTH_CONFIG.SCOPE,
    access_type: 'offline',
    prompt: 'consent'
  });

  return `https://accounts.google.com/o/oauth2/v2/auth?${params.toString()}`;
}

/**
 * 前端直接交换授权码获取令牌
 * 注意：这种方式会暴露客户端密钥，仅用于测试或网络受限环境
 */
export async function exchangeCodeForTokensFrontend(code: string): Promise<GoogleAuthResponse> {
  console.log('Frontend: Exchanging code for tokens...');
  
  try {
    // 注意：在生产环境中，这不是推荐的做法，因为会暴露客户端密钥
    // 但在开发环境或网络受限的情况下可以临时使用
    const response = await fetch('https://oauth2.googleapis.com/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        client_id: GOOGLE_OAUTH_CONFIG.CLIENT_ID,
        client_secret: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_SECRET || '', // 临时方案
        code,
        grant_type: 'authorization_code',
        redirect_uri: GOOGLE_OAUTH_CONFIG.REDIRECT_URI,
      }),
    });

    console.log('Frontend: Token response status:', response.status);

    if (!response.ok) {
      const errorData = await response.text();
      console.error('Frontend: Token exchange failed:', errorData);
      throw new Error(`Failed to exchange code for tokens: ${response.status} - ${errorData}`);
    }

    const tokens = await response.json();
    console.log('Frontend: Successfully received tokens');
    return tokens;
  } catch (error) {
    console.error('Frontend: Exchange tokens error:', error);
    throw error;
  }
}

/**
 * 使用访问令牌获取用户信息
 */
export async function getUserInfo(accessToken: string): Promise<GoogleUserInfo> {
  const response = await fetch(`https://www.googleapis.com/oauth2/v2/userinfo?access_token=${accessToken}`);
  
  if (!response.ok) {
    throw new Error('Failed to get user info');
  }

  return response.json();
}

/**
 * 在弹窗中打开Google OAuth登录
 */
export function openGoogleAuthPopup(): Promise<string> {
  return new Promise((resolve, reject) => {
    const authUrl = generateGoogleAuthUrl();
    console.log('Opening Google Auth URL:', authUrl);
    
    const popup = window.open(
      authUrl,
      'google-auth',
      'width=500,height=600,scrollbars=yes,resizable=yes'
    );

    if (!popup) {
      reject(new Error('无法打开弹窗，请检查浏览器弹窗设置'));
      return;
    }

    // 监听弹窗关闭或消息
    const checkClosed = setInterval(() => {
      if (popup.closed) {
        clearInterval(checkClosed);
        reject(new Error('用户取消了登录'));
      }
    }, 1000);

    // 监听来自弹窗的消息
    const messageListener = (event: MessageEvent) => {
      if (event.origin !== window.location.origin) {
        return;
      }

      console.log('Received message from popup:', event.data);

      if (event.data.type === 'GOOGLE_AUTH_SUCCESS') {
        clearInterval(checkClosed);
        window.removeEventListener('message', messageListener);
        popup.close();
        resolve(event.data.code);
      } else if (event.data.type === 'GOOGLE_AUTH_ERROR') {
        clearInterval(checkClosed);
        window.removeEventListener('message', messageListener);
        popup.close();
        reject(new Error(event.data.error || '登录失败'));
      }
    };

    window.addEventListener('message', messageListener);
  });
}

/**
 * 解析JWT token获取用户信息（用于id_token）
 */
export function parseJwtToken(token: string): any {
  try {
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
        .join('')
    );
    return JSON.parse(jsonPayload);
  } catch (error) {
    console.error('解析JWT token失败:', error);
    return null;
  }
}
