/**
 * Google Identity Services (GSI) 实现
 * 支持获取JWT格式的credential
 */

import { getGoogleClientId } from '../config/site-config';

// Google Identity Services 类型定义
declare global {
  interface Window {
    google: {
      accounts: {
        id: {
          initialize: (config: GoogleIdConfig) => void;
          prompt: () => void;
          renderButton: (element: HTMLElement, config: GoogleButtonConfig) => void;
          disableAutoSelect: () => void;
        };
      };
    };
  }
}

interface GoogleIdConfig {
  client_id: string;
  callback: (response: GoogleCredentialResponse) => void;
  auto_select?: boolean;
  cancel_on_tap_outside?: boolean;
}

interface GoogleButtonConfig {
  theme?: 'outline' | 'filled_blue' | 'filled_black';
  size?: 'large' | 'medium' | 'small';
  text?: 'signin_with' | 'signup_with' | 'continue_with' | 'signin';
  shape?: 'rectangular' | 'pill' | 'circle' | 'square';
  logo_alignment?: 'left' | 'center';
  width?: string;
  locale?: string;
}

interface GoogleCredentialResponse {
  credential: string; // JWT格式的凭证
  select_by?: string;
}

/**
 * 加载Google Identity Services脚本
 */
export function loadGoogleIdentityServices(): Promise<void> {
  return new Promise((resolve, reject) => {
    // 检查是否已经加载
    if (window.google?.accounts?.id) {
      resolve();
      return;
    }

    // 创建script标签
    const script = document.createElement('script');
    script.src = 'https://accounts.google.com/gsi/client';
    script.async = true;
    script.defer = true;
    
    script.onload = () => {
      // 等待Google对象完全初始化
      const checkGoogle = () => {
        if (window.google?.accounts?.id) {
          resolve();
        } else {
          setTimeout(checkGoogle, 100);
        }
      };
      checkGoogle();
    };
    
    script.onerror = () => {
      reject(new Error('Failed to load Google Identity Services'));
    };
    
    document.head.appendChild(script);
  });
}

/**
 * 使用Google Identity Services进行登录
 * 返回JWT格式的credential
 * @param siteId 站点ID，用于获取对应的Google Client ID
 */
export function signInWithGoogleGSI(siteId: number): Promise<string> {
  return new Promise(async (resolve, reject) => {
    try {
      // 确保Google Identity Services已加载
      await loadGoogleIdentityServices();

      // 获取站点对应的Google Client ID
      const clientId = await getGoogleClientId(siteId);
      console.log(`使用站点 ${siteId} 的Google Client ID: ${clientId.substring(0, 20)}...`);

      // 初始化Google登录
      window.google.accounts.id.initialize({
        client_id: clientId,
        callback: (response: GoogleCredentialResponse) => {
          if (response.credential) {
            resolve(response.credential);
          } else {
            reject(new Error('No credential received'));
          }
        },
        auto_select: false,
        cancel_on_tap_outside: false,
      });

      // 触发登录提示
      window.google.accounts.id.prompt();

    } catch (error) {
      reject(error);
    }
  });
}

/**
 * 在指定元素中渲染Google登录按钮
 * @param elementId 元素ID
 * @param siteId 站点ID，用于获取对应的Google Client ID
 * @param config 按钮配置
 */
export async function renderGoogleSignInButton(
  elementId: string,
  siteId: number,
  config: GoogleButtonConfig = {}
): Promise<void> {
  await loadGoogleIdentityServices();

  const element = document.getElementById(elementId);
  if (!element) {
    throw new Error(`Element with id '${elementId}' not found`);
  }

  // 获取站点对应的Google Client ID
  const clientId = await getGoogleClientId(siteId);
  console.log(`渲染Google登录按钮，使用站点 ${siteId} 的Client ID: ${clientId.substring(0, 20)}...`);

  const defaultConfig: GoogleButtonConfig = {
    theme: 'outline',
    size: 'large',
    text: 'signin_with',
    shape: 'rectangular',
    logo_alignment: 'left',
    width: '100%',
    ...config
  };

  return new Promise((resolve, reject) => {
    window.google.accounts.id.initialize({
      client_id: clientId,
      callback: (response: GoogleCredentialResponse) => {
        // 这里可以触发自定义事件或回调
        const event = new CustomEvent('googleSignIn', {
          detail: { credential: response.credential }
        });
        window.dispatchEvent(event);
        resolve();
      }
    });

    try {
      window.google.accounts.id.renderButton(element, defaultConfig);
      resolve();
    } catch (error) {
      reject(error);
    }
  });
}

/**
 * 检查Google Identity Services配置
 * @param siteId 站点ID
 * @param clientId 可选的Client ID，如果不传则从站点配置获取
 */
export async function checkGoogleConfig(siteId: number, clientId?: string): Promise<{ isValid: boolean; errors: string[] }> {
  const errors: string[] = [];

  try {
    // 获取Client ID
    const actualClientId = clientId || await getGoogleClientId(siteId);

    // 检查Client ID
    if (!actualClientId) {
      errors.push(`站点 ${siteId} 未配置Google Client ID`);
    } else if (!actualClientId.endsWith('.apps.googleusercontent.com')) {
      errors.push('Google Client ID格式不正确，应该以.apps.googleusercontent.com结尾');
    }

    // 检查当前域名
    const currentOrigin = typeof window !== 'undefined' ? window.location.origin : '';
    if (currentOrigin && !currentOrigin.startsWith('https://') && !currentOrigin.includes('localhost')) {
      errors.push('Google Identity Services要求使用HTTPS或localhost');
    }

    console.log(`Google配置检查 (站点 ${siteId}):`);
    console.log('Client ID:', actualClientId ? `${actualClientId.substring(0, 20)}...` : '未配置');
    console.log('当前域名:', currentOrigin);
    console.log('错误:', errors);

    return {
      isValid: errors.length === 0,
      errors
    };
  } catch (error) {
    console.error(`检查Google配置失败 (站点 ${siteId}):`, error);
    errors.push(`获取站点 ${siteId} 的Google配置失败: ${error instanceof Error ? error.message : '未知错误'}`);

    return {
      isValid: false,
      errors
    };
  }
}

/**
 * 解析JWT credential获取用户信息
 */
export function parseJWTCredential(credential: string): any {
  try {
    const base64Url = credential.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
        .join('')
    );
    return JSON.parse(jsonPayload);
  } catch (error) {
    console.error('解析JWT credential失败:', error);
    return null;
  }
}

/**
 * 一键直接触发Google登录（无中间弹窗）
 * @param siteId 站点ID，用于获取对应的Google Client ID
 */
export function signInWithGoogleButton(siteId: number): Promise<string> {
  return new Promise(async (resolve, reject) => {
    try {
      await loadGoogleIdentityServices();

      console.log(`初始化Google Identity Services一键登录 (站点 ${siteId})...`);

      // 获取站点对应的Google Client ID
      const clientId = await getGoogleClientId(siteId);
      console.log(`使用站点 ${siteId} 的Google Client ID: ${clientId.substring(0, 20)}...`);

      // 设置超时
      const timeoutId = setTimeout(() => {
        console.error('Google登录超时');
        reject(new Error('Google登录超时，请重试'));
      }, 30000);

      // 直接初始化并触发Google登录
      window.google.accounts.id.initialize({
        client_id: clientId,
        callback: (response: GoogleCredentialResponse) => {
          clearTimeout(timeoutId);
          console.log('Google登录回调触发:', response);
          if (response.credential) {
            console.log('Google登录成功，获得credential');
            resolve(response.credential);
          } else {
            console.error('未收到Google凭证:', response);
            reject(new Error('未收到Google凭证'));
          }
        },
        auto_select: false,
        cancel_on_tap_outside: true,
      });

      // 直接触发Google登录弹窗
      try {
        window.google.accounts.id.prompt((notification: any) => {
          console.log('Google登录提示状态:', notification);
          clearTimeout(timeoutId);

          if (notification.isNotDisplayed()) {
            const reason = notification.getNotDisplayedReason?.();
            console.log('Google登录提示未显示，原因:', reason);

            // 如果自动提示失败，创建一个临时的隐藏按钮来触发登录
            console.log('尝试使用隐藏按钮触发Google登录...');
            const tempContainer = document.createElement('div');
            tempContainer.style.position = 'fixed';
            tempContainer.style.top = '-1000px';
            tempContainer.style.left = '-1000px';
            tempContainer.style.visibility = 'hidden';
            tempContainer.id = 'temp-google-button-container';

            document.body.appendChild(tempContainer);

            // 重新初始化并渲染隐藏按钮
            window.google.accounts.id.initialize({
              client_id: clientId,
              callback: (response: GoogleCredentialResponse) => {
                // 清理临时容器
                if (document.body.contains(tempContainer)) {
                  document.body.removeChild(tempContainer);
                }
                if (response.credential) {
                  console.log('通过隐藏按钮获得Google credential');
                  resolve(response.credential);
                } else {
                  reject(new Error('未收到Google凭证'));
                }
              },
              auto_select: false,
              cancel_on_tap_outside: false,
            });

            // 渲染隐藏按钮并自动点击
            window.google.accounts.id.renderButton(tempContainer, {
              theme: 'outline',
              size: 'large',
              width: '200px'
            });

            // 延迟一点时间后自动点击按钮
            setTimeout(() => {
              const button = tempContainer.querySelector('div[role="button"]') as HTMLElement;
              if (button) {
                console.log('自动点击Google登录按钮');
                button.click();
              } else {
                // 清理并报错
                if (document.body.contains(tempContainer)) {
                  document.body.removeChild(tempContainer);
                }
                reject(new Error('无法触发Google登录，请检查配置'));
              }
            }, 100);

          } else if (notification.isSkippedMoment()) {
            const reason = notification.getSkippedReason?.();
            console.log('Google登录被跳过，原因:', reason);
            reject(new Error(`Google登录被跳过: ${reason || '未知原因'}`));
          } else if (notification.isDismissedMoment()) {
            const reason = notification.getDismissedReason?.();
            console.log('Google登录被关闭，原因:', reason);
            reject(new Error(`用户关闭了Google登录: ${reason || '用户取消'}`));
          }
        });
      } catch (error) {
        console.error('触发Google登录失败:', error);
        clearTimeout(timeoutId);
        reject(error);
      }

    } catch (error) {
      console.error('Google一键登录初始化失败:', error);
      reject(error);
    }
  });
}

/**
 * 备用的按钮式Google登录（如果一键登录失败时使用）
 * @param siteId 站点ID，用于获取对应的Google Client ID
 */
export function signInWithGoogleButtonFallback(siteId: number): Promise<string> {
  return new Promise(async (resolve, reject) => {
    try {
      await loadGoogleIdentityServices();

      console.log(`创建Google登录按钮 (站点 ${siteId})...`);

      // 获取站点对应的Google Client ID
      const clientId = await getGoogleClientId(siteId);
      console.log(`使用站点 ${siteId} 的Google Client ID: ${clientId.substring(0, 20)}...`);

      // 创建临时按钮容器，使用网站风格
      const container = document.createElement('div');
      container.style.position = 'fixed';
      container.style.top = '50%';
      container.style.left = '50%';
      container.style.transform = 'translate(-50%, -50%)';
      container.style.zIndex = '10000';
      container.style.background = 'linear-gradient(135deg, #18181b 0%, #09090b 100%)';
      container.style.padding = '24px';
      container.style.borderRadius = '16px';
      container.style.border = '1px solid #27272a';
      container.style.boxShadow = '0 25px 50px -12px rgba(0, 0, 0, 0.25)';
      container.style.minWidth = '320px';
      container.innerHTML = `
        <div style="text-align: center; margin-bottom: 20px;">
          <h3 style="margin: 0; color: #ffffff; font-size: 20px; font-weight: 600;">Google登录</h3>
          <p style="margin: 8px 0 0 0; color: #a1a1aa; font-size: 14px;">使用您的Google账号快速登录</p>
        </div>
        <div id="google-signin-button-temp"></div>
        <div style="text-align: center; margin-top: 16px;">
          <button id="cancel-button-temp" style="
            background: #27272a;
            border: 1px solid #3f3f46;
            color: #a1a1aa;
            padding: 8px 16px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s;
          " onmouseover="this.style.background='#3f3f46'" onmouseout="this.style.background='#27272a'">取消</button>
        </div>
      `;

      const overlay = document.createElement('div');
      overlay.style.position = 'fixed';
      overlay.style.top = '0';
      overlay.style.left = '0';
      overlay.style.width = '100%';
      overlay.style.height = '100%';
      overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.6)';
      overlay.style.backdropFilter = 'blur(4px)';
      overlay.style.zIndex = '9999';

      document.body.appendChild(overlay);
      document.body.appendChild(container);

      // 清理函数
      const cleanup = () => {
        if (document.body.contains(overlay)) document.body.removeChild(overlay);
        if (document.body.contains(container)) document.body.removeChild(container);
      };

      // 取消按钮
      const cancelButton = container.querySelector('#cancel-button-temp');
      if (cancelButton) {
        cancelButton.addEventListener('click', () => {
          cleanup();
          reject(new Error('用户取消了登录'));
        });
      }

      // 点击遮罩关闭
      overlay.onclick = () => {
        cleanup();
        reject(new Error('用户取消了登录'));
      };

      // 初始化Google登录
      window.google.accounts.id.initialize({
        client_id: clientId,
        callback: (response: GoogleCredentialResponse) => {
          cleanup();
          if (response.credential) {
            console.log('Google登录成功，获得credential');
            resolve(response.credential);
          } else {
            reject(new Error('未收到Google凭证'));
          }
        },
        auto_select: false,
        cancel_on_tap_outside: false,
      });

      // 渲染Google登录按钮
      const buttonElement = container.querySelector('#google-signin-button-temp') as HTMLElement;
      if (buttonElement) {
        window.google.accounts.id.renderButton(buttonElement, {
          theme: 'filled_blue',
          size: 'large',
          width: '272px',
          text: 'signin_with',
          shape: 'rectangular'
        });
      }

    } catch (error) {
      console.error('Google按钮登录初始化失败:', error);
      reject(error);
    }
  });
}

/**
 * 直接触发Google登录（一键登录）
 * @param siteId 站点ID，用于获取对应的Google Client ID
 */
export function signInWithGoogleDirect(siteId: number): Promise<string> {
  return new Promise(async (resolve, reject) => {
    try {
      await loadGoogleIdentityServices();

      // 获取站点对应的Google Client ID
      const clientId = await getGoogleClientId(siteId);

      console.log(`初始化Google Identity Services (站点 ${siteId})...`);
      console.log('Client ID:', `${clientId.substring(0, 20)}...`);
      console.log('当前域名:', window.location.origin);

      // 直接初始化并触发Google登录
      window.google.accounts.id.initialize({
        client_id: clientId,
        callback: (response: GoogleCredentialResponse) => {
          console.log('Google登录回调触发:', response);
          if (response.credential) {
            console.log('收到Google credential:', response.credential.substring(0, 50) + '...');
            resolve(response.credential);
          } else {
            console.error('未收到Google凭证:', response);
            reject(new Error('未收到Google凭证'));
          }
        },
        auto_select: false,
        cancel_on_tap_outside: true,
      });

      // 设置错误处理和超时
      const timeoutId = setTimeout(() => {
        console.error('Google登录超时');
        reject(new Error('Google登录超时，请重试'));
      }, 30000); // 30秒超时

      // 直接触发Google登录弹窗
      try {
        window.google.accounts.id.prompt((notification: any) => {
          console.log('Google登录提示状态:', notification);
          console.log('notification详细信息:', {
            isNotDisplayed: notification.isNotDisplayed?.(),
            isSkippedMoment: notification.isSkippedMoment?.(),
            isDismissedMoment: notification.isDismissedMoment?.(),
            getMomentType: notification.getMomentType?.(),
            getNotDisplayedReason: notification.getNotDisplayedReason?.(),
            getSkippedReason: notification.getSkippedReason?.(),
            getDismissedReason: notification.getDismissedReason?.()
          });

          clearTimeout(timeoutId);

          if (notification.isNotDisplayed()) {
            const reason = notification.getNotDisplayedReason?.();
            console.log('Google登录提示未显示，原因:', reason);
            console.log('可能的解决方案：');
            console.log('1. 检查Google Cloud Console中的授权JavaScript来源');
            console.log('2. 确认Client ID格式正确');
            console.log('3. 清除浏览器缓存和Cookie');
            console.log('4. 尝试无痕模式');
            reject(new Error(`Google登录提示未显示，原因: ${reason || '未知'}`));
          } else if (notification.isSkippedMoment()) {
            const reason = notification.getSkippedReason?.();
            console.log('Google登录被跳过，原因:', reason);
            reject(new Error(`Google登录被跳过，原因: ${reason || '未知'}`));
          } else if (notification.isDismissedMoment()) {
            const reason = notification.getDismissedReason?.();
            console.log('Google登录被关闭，原因:', reason);
            reject(new Error(`Google登录被关闭，原因: ${reason || '用户关闭'}`));
          }
        });
      } catch (error) {
        console.error('触发Google登录失败:', error);
        clearTimeout(timeoutId);
        reject(error);
      }

    } catch (error) {
      console.error('Google登录初始化失败:', error);
      reject(error);
    }
  });
}

/**
 * 弹窗式Google登录（使用GSI）- 保留原版本以备用
 * @param siteId 站点ID，用于获取对应的Google Client ID
 */
export function openGoogleSignInPopup(siteId: number): Promise<string> {
  return new Promise(async (resolve, reject) => {
    try {
      await loadGoogleIdentityServices();

      // 获取站点对应的Google Client ID
      const clientId = await getGoogleClientId(siteId);
      console.log(`弹窗式Google登录，使用站点 ${siteId} 的Client ID: ${clientId.substring(0, 20)}...`);

      // 创建一个临时的容器元素
      const container = document.createElement('div');
      container.style.position = 'fixed';
      container.style.top = '50%';
      container.style.left = '50%';
      container.style.transform = 'translate(-50%, -50%)';
      container.style.zIndex = '10000';
      container.style.backgroundColor = 'white';
      container.style.padding = '20px';
      container.style.borderRadius = '8px';
      container.style.boxShadow = '0 4px 6px rgba(0, 0, 0, 0.1)';
      container.id = 'google-signin-popup';

      const overlay = document.createElement('div');
      overlay.style.position = 'fixed';
      overlay.style.top = '0';
      overlay.style.left = '0';
      overlay.style.width = '100%';
      overlay.style.height = '100%';
      overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
      overlay.style.zIndex = '9999';

      document.body.appendChild(overlay);
      document.body.appendChild(container);

      // 清理函数
      const cleanup = () => {
        document.body.removeChild(overlay);
        document.body.removeChild(container);
      };

      // 点击遮罩关闭
      overlay.onclick = () => {
        cleanup();
        reject(new Error('用户取消了登录'));
      };

      // 初始化Google登录
      window.google.accounts.id.initialize({
        client_id: clientId,
        callback: (response: GoogleCredentialResponse) => {
          cleanup();
          if (response.credential) {
            resolve(response.credential);
          } else {
            reject(new Error('No credential received'));
          }
        }
      });
      
      // 渲染登录按钮
      container.innerHTML = `
        <div style="text-align: center; margin-bottom: 15px;">
          <h3 style="margin: 0; color: #333;">使用Google账号登录</h3>
        </div>
        <div id="google-signin-button"></div>
        <div style="text-align: center; margin-top: 15px;">
          <button id="cancel-button" style="background: #f5f5f5; border: 1px solid #ddd; padding: 8px 16px; border-radius: 4px; cursor: pointer;">取消</button>
        </div>
      `;
      
      // 取消按钮事件
      const cancelButton = container.querySelector('#cancel-button');
      if (cancelButton) {
        cancelButton.addEventListener('click', () => {
          cleanup();
          reject(new Error('用户取消了登录'));
        });
      }
      
      // 渲染Google登录按钮
      const buttonElement = container.querySelector('#google-signin-button') as HTMLElement;
      if (buttonElement) {
        window.google.accounts.id.renderButton(buttonElement, {
          theme: 'outline',
          size: 'large',
          width: '300px'
        });
      }
      
    } catch (error) {
      reject(error);
    }
  });
}
