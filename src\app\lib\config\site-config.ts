/**
 * 站点配置管理
 * 支持获取和缓存不同游戏的配置信息
 */

import { API_ENDPOINTS } from '../../config/api';

// 登录类型枚举
export enum LoginType {
  GOOGLE = 1,
  FACEBOOK = 2
}

// 站点登录配置接口
export interface SiteLoginConfig {
  id: number;
  sitePayId: number;
  type: LoginType;
  clientId: string;
  creator: string;
  createTime: number;
  updater: string;
  updateTime: number;
  deleted: boolean;
}

// 站点配置响应接口
export interface SiteConfigResponse {
  code: number;
  data: {
    siteId: number;
    sitePayLoginList: SiteLoginConfig[];
    siteName: string | null;
  };
  msg: string;
}

// 缓存的配置数据
interface CachedSiteConfig {
  siteId: number;
  googleClientId: string | null;
  facebookClientId: string | null;
  siteName: string | null;
  timestamp: number;
}

// 配置缓存（内存缓存）
const configCache = new Map<number, CachedSiteConfig>();

// 缓存有效期（5分钟）
const CACHE_DURATION = 5 * 60 * 1000;

/**
 * 获取站点配置
 * @param siteId 站点ID
 * @param forceRefresh 是否强制刷新缓存
 * @returns 站点配置
 */
export async function getSiteConfig(siteId: number, forceRefresh = false): Promise<CachedSiteConfig> {
  // 检查缓存
  if (!forceRefresh) {
    const cached = configCache.get(siteId);
    if (cached && (Date.now() - cached.timestamp) < CACHE_DURATION) {
      console.log(`使用缓存的站点配置 (siteId: ${siteId}):`, cached);
      return cached;
    }
  }

  try {
    console.log(`获取站点配置 (siteId: ${siteId})...`);
    
    const response = await fetch(`${API_ENDPOINTS.GET_SITE_CONFIG}?siteId=${siteId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result: SiteConfigResponse = await response.json();
    
    if (result.code !== 0) {
      throw new Error(`API error: ${result.msg || '获取站点配置失败'}`);
    }

    // 解析配置数据
    const { data } = result;
    let googleClientId: string | null = null;
    let facebookClientId: string | null = null;

    // 从登录配置列表中提取不同类型的客户端ID
    if (data.sitePayLoginList && Array.isArray(data.sitePayLoginList)) {
      for (const loginConfig of data.sitePayLoginList) {
        if (loginConfig.type === LoginType.GOOGLE && loginConfig.clientId) {
          googleClientId = loginConfig.clientId;
        } else if (loginConfig.type === LoginType.FACEBOOK && loginConfig.clientId) {
          facebookClientId = loginConfig.clientId;
        }
      }
    }

    // 创建缓存数据
    const cachedConfig: CachedSiteConfig = {
      siteId: data.siteId,
      googleClientId,
      facebookClientId,
      siteName: data.siteName,
      timestamp: Date.now()
    };

    // 更新缓存
    configCache.set(siteId, cachedConfig);
    
    console.log(`站点配置获取成功 (siteId: ${siteId}):`, {
      googleClientId: googleClientId ? `${googleClientId.substring(0, 20)}...` : null,
      facebookClientId: facebookClientId ? `${facebookClientId.substring(0, 20)}...` : null,
      siteName: cachedConfig.siteName
    });

    return cachedConfig;

  } catch (error) {
    console.error(`获取站点配置失败 (siteId: ${siteId}):`, error);
    
    // 如果有缓存数据，即使过期也返回
    const cached = configCache.get(siteId);
    if (cached) {
      console.warn(`使用过期的缓存配置 (siteId: ${siteId})`);
      return cached;
    }
    
    throw error;
  }
}

/**
 * 获取Google Client ID
 * @param siteId 站点ID
 * @returns Google Client ID
 */
export async function getGoogleClientId(siteId: number): Promise<string> {
  const config = await getSiteConfig(siteId);
  
  if (!config.googleClientId) {
    throw new Error(`站点 ${siteId} 未配置Google Client ID`);
  }
  
  return config.googleClientId;
}

/**
 * 获取Facebook Client ID
 * @param siteId 站点ID
 * @returns Facebook Client ID
 */
export async function getFacebookClientId(siteId: number): Promise<string> {
  const config = await getSiteConfig(siteId);
  
  if (!config.facebookClientId) {
    throw new Error(`站点 ${siteId} 未配置Facebook Client ID`);
  }
  
  return config.facebookClientId;
}

/**
 * 清除指定站点的配置缓存
 * @param siteId 站点ID，如果不传则清除所有缓存
 */
export function clearSiteConfigCache(siteId?: number): void {
  if (siteId !== undefined) {
    configCache.delete(siteId);
    console.log(`已清除站点 ${siteId} 的配置缓存`);
  } else {
    configCache.clear();
    console.log('已清除所有站点配置缓存');
  }
}

/**
 * 预加载站点配置
 * @param siteId 站点ID
 */
export async function preloadSiteConfig(siteId: number): Promise<void> {
  try {
    await getSiteConfig(siteId);
    console.log(`站点 ${siteId} 配置预加载完成`);
  } catch (error) {
    console.error(`站点 ${siteId} 配置预加载失败:`, error);
  }
}

/**
 * 检查站点是否支持指定的登录方式
 * @param siteId 站点ID
 * @param loginType 登录类型
 * @returns 是否支持
 */
export async function isSiteLoginSupported(siteId: number, loginType: LoginType): Promise<boolean> {
  try {
    const config = await getSiteConfig(siteId);
    
    switch (loginType) {
      case LoginType.GOOGLE:
        return !!config.googleClientId;
      case LoginType.FACEBOOK:
        return !!config.facebookClientId;
      default:
        return false;
    }
  } catch (error) {
    console.error(`检查站点 ${siteId} 登录支持失败:`, error);
    return false;
  }
}
