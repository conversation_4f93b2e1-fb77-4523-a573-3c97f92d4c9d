import axios from 'axios';
import { API_ENDPOINTS, SITE_ID } from '../../config/api';
import { ApiGame, GetSiteInfoResponse, ServerRoleInfo } from './types';
import { APP_API_BASE_URL } from '../../config/api';
// 定义API响应类型
interface ApiResponse<T> {
  code: number;
  data: T;
  msg: string;
}

// 定义站点信息类型
export interface SiteInfo {
  id: number;
  i18n: {
    title: Record<string, string>;
    description: Record<string, string>;
    [key: string]: Record<string, string>;
  };
  gameList?: ApiGame[]; // 游戏列表
  game?: ApiGame; // 游戏详情
  createdAt?: string;
  updatedAt?: string;
  // 可能的其他字段
  siteConfig?: Record<string, unknown>;
  payConfig?: Record<string, unknown>;
}

// 定义游戏列表项类型
export interface GameListItem {
  id: number;
  name: string;
  url: string;
  iconUrl: string;
  bannerUrl: string;
  rating: number;
  playerCount: string;
  status: number;
  callBackUrl: string;
  serverUrl: string;
  loginUrl: string;
  payUrl: string;
  creator: string;
  createTime: number;
  updater: string;
  updateTime: number;
  deleted: boolean;
  tenantId: number;
  i18ns?: GameI18nItem[]; // 新增国际化数组
}

// 定义游戏国际化项类型
export interface GameI18nItem {
  id: number;
  siteId: number;
  locale: string;
  title: string;
  description: string;
  category: string;
  createTime: number;
  updater: string;
  updateTime: number;
  deleted: boolean;
  tenantId: number;
}

// 缓存对象，用于存储已获取的游戏数据，避免重复请求
const gameDataCache: Record<string, {
  data: ApiGame;
  timestamp: number;
}> = {};

// 缓存过期时间（5分钟）
const CACHE_EXPIRY = 5 * 60 * 1000;

/**
 * 获取站点信息和游戏详情
 * @param gameId 游戏ID（可选，如果提供则返回该游戏详情）
 * @returns 站点信息和游戏详情
 */
export async function fetchSiteInfo(gameId?: string) {
  try {
    const response = await axios.get<ApiResponse<ApiGame | SiteInfo>>(API_ENDPOINTS.GET_SITE_INFO, {
      params: { 
        id: gameId || undefined 
      }
    });
    
    if (response.data.code === 0) {
      // 如果是游戏详情，更新缓存
      if (gameId && response.data.data) {
        const data = response.data.data;
        if ('game' in data && data.game) {
          gameDataCache[gameId] = {
            data: data.game as ApiGame,
            timestamp: Date.now()
          };
        } else if ('id' in data) {
          gameDataCache[gameId] = {
            data: data as ApiGame,
            timestamp: Date.now()
          };
        }
      }
      return response.data.data;
    } else {
      throw new Error(response.data.msg || '获取站点信息失败');
    }
  } catch (error) {
    console.error('获取站点信息失败:', error);
    throw error;
  }
}

/**
 * 从API获取所有游戏数据
 * @returns 包含多语言的游戏数据
 */
export async function fetchGamesData(): Promise<GameListItem[]> {
  try {
    // 尝试从实际API获取数据
    const response = await axios.get<ApiResponse<GameListItem[]>>(API_ENDPOINTS.GET_GAMES_LIST, {
      params: { siteId: SITE_ID }
    });
    
    if (response.data.code === 0 && response.data.data) {
      return response.data.data;
    } else {
      throw new Error(response.data.msg || '获取游戏列表失败');
    }
  } catch (error) {
    console.error('获取游戏列表失败:', error);
    throw error; // 直接抛出错误，不使用模拟数据
  }
}

/**
 * 从API获取单个游戏数据
 * @param gameId 游戏ID
 * @returns 包含多语言的游戏数据
 */
export async function fetchGameData(gameId: string): Promise<ApiGame> {
  // 检查缓存中是否有有效数据
  const cachedData = gameDataCache[gameId];
  if (cachedData && (Date.now() - cachedData.timestamp < CACHE_EXPIRY)) {
   
    return cachedData.data;
  }
  
  try {
    // 尝试从实际API获取数据
    const response = await axios.get<ApiResponse<ApiGame | SiteInfo>>(API_ENDPOINTS.GET_SITE_INFO, {
      params: { 
        id: gameId
      }
    });
    
    if (response.data.code === 0 && response.data.data) {
      const gameData = response.data.data;
      let apiGame: ApiGame;
      
      // 检查返回的数据格式，适配不同的响应结构
      if ('game' in gameData && gameData.game) {
        // 旧格式: 返回的是 SiteInfo 对象，包含 game 属性
        apiGame = gameData.game as ApiGame;
      } else if ('id' in gameData && (('i18n' in gameData) || ('i18nObj' in gameData) || ('i18ns' in gameData))) {
        // 新格式: 直接返回游戏对象
        apiGame = gameData as ApiGame;
      } else {
        console.error('无法识别的游戏数据格式:', gameData);
        throw new Error('获取游戏详情失败: 格式错误');
      }
      
      // 更新缓存
      gameDataCache[gameId] = {
        data: apiGame,
        timestamp: Date.now()
      };
      
      return apiGame;
    } else {
      throw new Error(response.data.msg || '获取游戏详情失败');
    }
  } catch (error) {
    console.error(`获取游戏 ${gameId} 详情失败:`, error);
    throw error; // 直接抛出错误，不使用模拟数据
  }
}

// 定义支付参数接口
export interface PaymentParams {
  productCode: string;
  goodsId: string;
  subject: string;
  roleId: string;
  serverId: string;
  roleName: string;
  uid: string;
  ulang?: string;
  paymentMethod?: string; // 添加支付方式参数
}

// 定义支付响应接口
export interface PaymentResponse {
  status: boolean;
  message?: string;
  data: {
    payUrl: string;
  };
}

// 定义确认支付请求参数接口
export interface ConfirmPaymentParams {
  siteId: number;
  configId: number;
  methodId: number;
  amount: number;
  currency: string;
  productName: string;
  skuCode: string;
  userId: string;
  roleId: string;
  serverId: string;
  roleName: string;
  returnUrl: string;
}

// 定义确认支付响应接口
export interface ConfirmPaymentResponse {
  code: number;
  data: {
    paymentUrl: string;
    paymentMethod: string;
    paymentOrderNo: string;
    thirdPartyOrderNo: string;
    status: string;
    expireTime: string;
    extraInfo: string;
  };
  msg: string;
}

/**
 * 发起支付请求
 * @param params 支付参数
 * @returns 支付响应
 */
export async function doPayment(params: PaymentParams): Promise<PaymentResponse> {
  try {
    // 创建FormData对象
    const formData = new FormData();

    // 添加支付参数
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        formData.append(key, value);
      }
    });

    // 调用内部API路由
    const response = await fetch('/api/payment', {
      method: 'POST',
      body: formData
    });

    const data = await response.json();
    return data as PaymentResponse;
  } catch (error) {
    console.error('支付请求失败:', error);
    throw new Error('支付请求失败');
  }
}

/**
 * 获取用户的区服和角色信息
 * @param serverUrl 服务器URL
 * @param userInfo 用户信息
 * @returns 区服和角色信息列表
 */
export async function getUserSiteInfo(serverUrl: string, userInfo: { uid: number; username: string; productCode: string; callbackKey: string }): Promise<ServerRoleInfo[]> {
  try {
    // 准备请求参数
    const params: Record<string, string> = {
      productCode: userInfo.productCode,
      frType: '1', // 默认使用frType=1
      uid: userInfo.uid.toString(),
      username: userInfo.username
    };

    // 计算签名 - 使用与LoginModal相同的签名算法
    const keys = Object.keys(params).sort();
    let signKey = '';
    for (const key of keys) {
      signKey += `${key}=${params[key]}&`;
    }
    signKey += userInfo.callbackKey;

    // 计算MD5值 - 需要导入md5
    const md5 = require('crypto-js/md5');
    const sign = md5(signKey).toString();

    // 添加签名到参数中
    params.sign = sign;

    // 创建 FormData
    const formData = new FormData();
    Object.entries(params).forEach(([key, value]) => {
      formData.append(key, value);
    });

    const response = await axios.post<GetSiteInfoResponse>(serverUrl, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    if (response.data.code === 200 && response.data.data) {
      return response.data.data;
    } else {
      throw new Error('获取区服角色信息失败');
    }
  } catch (error) {
    console.error('获取区服角色信息失败:', error);
    throw error;
  }
}

// 定义支付配置接口
export interface PaymentMethod {
  id: number;
  configId: number;
  methodId: number;
  methodCode: string;
  methodName: string;
  sort: number;
  iconUrl: string; // 添加iconUrl字段到PaymentMethod
}

export interface PaymentConfig {
  id: number;
  name: string;
  provider: string;
  sort: number;
  methods: PaymentMethod[];
}

export interface PaymentConfigResponse {
  code: number;
  data: PaymentConfig[];
  msg: string;
}

/**
 * 获取支付配置列表
 * @param siteId 站点ID，不同游戏使用不同的siteId
 * @returns 支付配置列表
 */
export async function fetchPaymentConfigs(siteId?: number): Promise<PaymentConfig[]> {
  try {
    const response = await axios.get<PaymentConfigResponse>(`${API_ENDPOINTS.GET_PAYMENT_CONFIGS}`, {
      params: {
        siteId: siteId || SITE_ID // 使用传入的 siteId，如果没有则使用默认值
      }
    });

    if (response.data.code === 0) {
      return response.data.data;
    } else {
      throw new Error(response.data.msg || '获取支付配置失败');
    }
  } catch (error) {
    console.error('获取支付配置失败:', error);
    throw error;
  }
}

/**
 * 确认支付
 * @param params 确认支付参数
 * @returns 确认支付响应
 */
export async function confirmPayment(params: ConfirmPaymentParams): Promise<ConfirmPaymentResponse> {
  try {
    // 调用内部API路由，避免CORS问题
    const response = await fetch(`${APP_API_BASE_URL}/pay/confirm-payment`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(params),
    });

    const data = await response.json();

    if (data.code === 0) {
      return data;
    } else {
      throw new Error(data.msg || '确认支付失败');
    }
  } catch (error) {
    console.error('确认支付失败:', error);
    throw error;
  }
}